import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getToken } from 'next-auth/jwt';

// Helper function to check if user is authenticated and has admin or system_admin role
async function isAuthorizedAdmin(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token) return false;

    // Check if the user exists and has admin or system_admin role
    const user = await prisma.user.findUnique({
      where: { Email: token.email as string },
    });

    return user?.Role === 'admin' || user?.Role === 'system_admin';
  } catch (error) {
    console.error('Auth check error:', error);
    return false;
  }
}

// Helper function to get the current user from the request
async function getCurrentUser(req: NextRequest) {
  try {
    const token = await getToken({ req });
    if (!token || !token.email) return null;

    const user = await prisma.user.findFirst({
      where: {
        Email: token.email,
        IsDeleted: false,
      },
    });

    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// GET: list all user-warehouse relations
export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Check if user is authorized as admin or system_admin
    if (currentUser.Role !== 'admin' && currentUser.Role !== 'system_admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // If user is system_admin, return all user-warehouse relations
    if (currentUser.Role === 'system_admin') {
      const userWarehouseList = await prisma.userWarehouse.findMany();
      return NextResponse.json(userWarehouseList, { status: 200 });
    }

    // If user is admin, only return user-warehouse relations for warehouses they're assigned to
    // First, get the warehouses the admin is assigned to
    const adminWarehouses = await prisma.userWarehouse.findMany({
      where: { UserId: currentUser.UserId },
      select: { WarehouseId: true },
    });

    const warehouseIds = adminWarehouses.map((w) => w.WarehouseId);

    // Then, get all user-warehouse relations for those warehouses
    const userWarehouseList = await prisma.userWarehouse.findMany({
      where: {
        WarehouseId: { in: warehouseIds },
      },
    });

    return NextResponse.json(userWarehouseList, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: assign a user to a warehouse
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Check if user is authorized as admin or system_admin
    if (currentUser.Role !== 'admin' && currentUser.Role !== 'system_admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    const { UserId, WarehouseId } = await request.json();

    // If user is admin, verify they have access to the warehouse they're trying to assign
    if (currentUser.Role === 'admin') {
      // Check if the admin is assigned to this warehouse
      const adminHasAccess = await prisma.userWarehouse.findFirst({
        where: {
          UserId: currentUser.UserId,
          WarehouseId: WarehouseId,
        },
      });

      if (!adminHasAccess) {
        return NextResponse.json(
          {
            error: 'You can only assign users to warehouses you have access to',
          },
          { status: 403 }
        );
      }

      // Verify the user being assigned is not a system_admin
      const targetUser = await prisma.user.findUnique({
        where: { UserId },
      });

      if (targetUser?.Role === 'system_admin') {
        return NextResponse.json(
          {
            error:
              'Regular administrators cannot modify system administrator assignments',
          },
          { status: 403 }
        );
      }
    }

    // Create the assignment
    const newAssignment = await prisma.userWarehouse.create({
      data: { UserId, WarehouseId },
    });
    return NextResponse.json(newAssignment, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE: remove a user from a warehouse
export async function DELETE(request: NextRequest) {
  try {
    // Get the current user
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Check if user is authorized as admin or system_admin
    if (currentUser.Role !== 'admin' && currentUser.Role !== 'system_admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    const { UserId, WarehouseId } = await request.json();

    // If user is admin, verify they have access to the warehouse they're trying to modify
    if (currentUser.Role === 'admin') {
      // Check if the admin is assigned to this warehouse
      const adminHasAccess = await prisma.userWarehouse.findFirst({
        where: {
          UserId: currentUser.UserId,
          WarehouseId: WarehouseId,
        },
      });

      if (!adminHasAccess) {
        return NextResponse.json(
          {
            error:
              'You can only modify warehouse assignments for warehouses you have access to',
          },
          { status: 403 }
        );
      }

      // Verify the user being modified is not a system_admin
      const targetUser = await prisma.user.findUnique({
        where: { UserId },
      });

      if (targetUser?.Role === 'system_admin') {
        return NextResponse.json(
          {
            error:
              'Regular administrators cannot modify system administrator assignments',
          },
          { status: 403 }
        );
      }
    }

    // Delete the assignment
    await prisma.userWarehouse.deleteMany({
      where: { UserId, WarehouseId },
    });
    return NextResponse.json(
      { message: 'Assignment removed' },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
