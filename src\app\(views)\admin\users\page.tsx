'use client';

import useS<PERSON> from 'swr';
import { fetchUsers, createUser, updateUser } from '../admin.services';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { useRef, useState } from 'react';
import { InputText } from 'primereact/inputtext';
import { textEditor, dropdownEditor } from '../../receipts/utils/editors.utils';
import { Role } from '@/app/shared/models/global.enums';
import ReusableToast from '@/app/shared/components/ReusableToast';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import { Dropdown } from 'primereact/dropdown';
import { useWarehouseStore } from '@/store';
import { useAuthStore } from '@/store/auth.store';
import { isValidEmail } from '@/lib/validation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';

function UsersManagement() {
  const { data: users, error, mutate } = useSWR('/admin/users', fetchUsers);
  const [newUser, setNewUser] = useState({ Name: '', Email: '', Role: '' });
  // Define all possible roles
  const allRoles = Object.values(Role).map((r) => ({ label: r, value: r }));
  const toastRef = useRef<any>(null);
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const { user: loggedInUser } = useAuthStore();
  const isUserLoading = useAuthStore((state) => state.isUserLoading);

  // Filter out the current user from the users list
  const filteredUsers = users?.filter(
    (user: any) => user.UserId !== loggedInUser?.UserId
  );

  const onRowEditComplete = async (e: any) => {
    try {
      const { UserId, Name, Role } = e.newData;

      // Prevent modifying own role
      if (UserId === loggedInUser?.UserId && Role !== e.data.Role) {
        toastRef.current?.show({
          severity: 'error',
          summary: 'Error',
          detail: 'You cannot change your own role.',
        });
        setEditingRowId(null);
        return;
      }

      await updateUser(UserId, { Name, Role });

      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'User updated',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update user',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const onRowEditInit = (e: any) => {
    setEditingRowId(e.data.UserId);
  };

  const onRowEditCancel = () => {
    setEditingRowId(null);
  };

  const handleDelete = async (rowData: any) => {
    if (rowData.UserId === loggedInUser?.UserId) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'You cannot delete your own account.',
      });
      return;
    }

    try {
      await updateUser(rowData.UserId, { IsDeleted: true }); // soft-delete
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'User deleted',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete user',
      });
    }
  };

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  const handleCreate = async () => {
    // Validate all fields are provided
    if (!newUser.Name || !newUser.Email || !newUser.Role) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'All fields are mandatory',
      });
      return;
    }
    try {
      await createUser(newUser);
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'User created',
      });
      setNewUser({ Name: '', Email: '', Role: '' });
      mutate();
    } catch (err) {
      console.error('Failed to create user:', err);
    }
  };

  if (isUserLoading) return <p>Loading user data...</p>;
  if (!users) return <p>Loading users...</p>;
  if (error) return <p>Error loading users</p>;

  return (
    <>
      <ReusableToast ref={toastRef} />
      <h1>Users Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          type="text"
          placeholder="Name"
          value={newUser.Name}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setNewUser({ ...newUser, Name: e.target.value })
          }
        />
        <InputText
          type="email"
          placeholder="Email"
          value={newUser.Email}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setNewUser({ ...newUser, Email: e.target.value })
          }
          invalid={newUser.Email ? !isValidEmail(newUser.Email) : false}
        />
        <Dropdown
          value={newUser.Role}
          options={
            loggedInUser?.Role === 'system_admin'
              ? allRoles
              : allRoles.filter((r) => r.value !== 'system_admin')
          }
          placeholder="Select role"
          onChange={(e: { value: string }) =>
            setNewUser({ ...newUser, Role: e.value })
          }
        />
        <Button
          disabled={
            !newUser.Name ||
            !newUser.Email ||
            !newUser.Role ||
            (!!newUser.Email && !isValidEmail(newUser.Email))
          }
          label="Create User"
          onClick={handleCreate}
        />
      </div>
      <DataTable
        value={filteredUsers}
        editMode="row"
        dataKey="UserId"
        size="small"
        stripedRows
        onRowEditComplete={onRowEditComplete}
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
      >
        <Column
          field="Name"
          header="Name"
          editor={(options) => textEditor(options)}
        />
        <Column
          field="Email"
          header="Email"
          style={{ width: '550px' }}
          editor={(options) => textEditor(options)}
        />
        <Column
          field="Role"
          header="Role"
          editor={(options) => {
            const isEditingOwnRow =
              options.rowData.UserId === loggedInUser?.UserId;

            // Filter out system_admin option for non-system_admin users
            const availableRoles =
              loggedInUser?.Role === 'system_admin'
                ? allRoles
                : allRoles.filter((r) => r.value !== 'system_admin');

            return (
              <Dropdown
                value={options.value}
                options={availableRoles}
                optionLabel="label"
                optionValue="value"
                placeholder="Select role"
                disabled={isEditingOwnRow} // Disable for self
                onChange={(e) => options.editorCallback?.(e.value)}
                className={isEditingOwnRow ? 'opacity-50' : ''}
              />
            );
          }}
          body={(rowData) => <span>{rowData.Role}</span>} // Show text when not editing
        />

        <Column rowEditor headerStyle={{ width: '4rem' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
        />
      </DataTable>

      <ReusableConfirmDialog />
    </>
  );
}

export default UsersManagement;
