'use client';

import useS<PERSON> from 'swr';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { useRef, useState } from 'react';
import ReusableToast from '@/app/shared/components/ReusableToast';
import {
  fetchWarehouses,
  createWarehouse,
  updateWarehouse,
  deleteWarehouse,
  fetchUsers,
  fetchUserWarehouseAssignments,
  assignUserToWarehouse,
  removeUserFromWarehouse,
} from '../admin.services';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import { useWarehouseStore } from '@/store';
import { useAuthStore } from '@/store';
import ReusableDialog from '@/app/shared/components/ReusableDialog';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';

function WarehousesManagement() {
  const userEmail = useAuthStore((state) => state.user?.Email);
  const isUserLoading = useAuthStore((state) => state.isUserLoading);

  const {
    data: warehouses,
    error,
    mutate,
  } = useSWR(
    userEmail ? '/admin/warehouses' : null,
    userEmail ? fetchWarehouses : null
  );

  const [newWarehouse, setNewWarehouse] = useState({ Name: '' });
  const toastRef = useRef<any>(null);
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const { retriggerFetchWarehouses } = useWarehouseStore();
  const [assignUsersDialogVisible, setAssignUsersDialogVisible] =
    useState(false);
  const [warehouseToAssign, setWarehouseToAssign] = useState<any>(null);

  const onRowEditInit = (event: any) => {
    setEditingRowId(event.data ? event.data.WarehouseId : event.WarehouseId);
  };

  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateWarehouse(e.newData.WarehouseId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Warehouse updated',
      });
      mutate();
      retriggerFetchWarehouses();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update warehouse',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleDelete = async (rowData: any) => {
    try {
      await deleteWarehouse(rowData.WarehouseId);
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Warehouse deleted',
      });
      mutate();
      retriggerFetchWarehouses();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete warehouse',
      });
    }
  };

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  const handleCreate = async () => {
    try {
      await createWarehouse(newWarehouse);
      setNewWarehouse({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Warehouse created',
      });
      mutate();
      retriggerFetchWarehouses();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create warehouse',
      });
    }
  };

  const shouldFetchUsers = assignUsersDialogVisible && warehouseToAssign;
  const { data: users, mutate: mutateUsers } = useSWR(
    shouldFetchUsers ? '/admin/users' : null,
    shouldFetchUsers ? fetchUsers : null
  );
  const { data: assignments, mutate: mutateAssignments } = useSWR(
    shouldFetchUsers ? 'user-warehouse-assignments' : null,
    shouldFetchUsers ? () => fetchUserWarehouseAssignments('all') : null
  );

  // Filter out the current user from the users list
  const filteredUsers = users?.filter((user: any) => user.Email !== userEmail);

  const openAssignUsersDialog = (rowData: any) => {
    setWarehouseToAssign(rowData);
    setAssignUsersDialogVisible(true);
    mutateUsers();
    mutateAssignments();
  };

  const isUserAssigned = (userId: string) => {
    return assignments?.some(
      (a: any) =>
        a.UserId === userId && a.WarehouseId === warehouseToAssign?.WarehouseId
    );
  };

  const handleAssign = async (userId: string) => {
    if (!warehouseToAssign) return;
    await assignUserToWarehouse({
      UserId: userId,
      WarehouseId: warehouseToAssign.WarehouseId,
    });
    mutateAssignments();
  };

  const handleRemove = async (userId: string) => {
    if (!warehouseToAssign) return;
    await removeUserFromWarehouse({
      UserId: userId,
      WarehouseId: warehouseToAssign.WarehouseId,
    });
    mutateAssignments();
  };

  if (isUserLoading) return <p>Loading user data...</p>;
  if (!warehouses) return <p>Loading warehouses...</p>;
  if (error) return <p>Error loading warehouses</p>;

  return (
    <>
      <h1>Warehouses Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          type="text"
          placeholder="Name"
          value={newWarehouse.Name}
          onChange={(e) => setNewWarehouse({ Name: e.target.value })}
        />
        <Button
          disabled={!newWarehouse.Name}
          label="Create Warehouse"
          onClick={handleCreate}
        />
      </div>
      <DataTable
        value={warehouses}
        editMode="row"
        dataKey="WarehouseId"
        size="small"
        stripedRows
        onRowEditComplete={onRowEditComplete}
        onRowEditCancel={onRowEditCancel}
        onRowEditInit={onRowEditInit}
      >
        <Column
          field="Name"
          header="Name"
          editor={(options) => textEditor(options)}
        />
        <Column rowEditor headerStyle={{ width: '4rem' }} bodyStyle={{ textAlign: 'center' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '4rem' }}
          bodyStyle={{ textAlign: 'center' }}
        />
        <Column
          body={(rowData) => (
            <Button
              label="Users"
              onClick={() => openAssignUsersDialog(rowData)}
              className="p-button-sm"
              tooltip="Assign Users"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
          bodyStyle={{ textAlign: 'center' }}
        />
      </DataTable>

      <ReusableDialog
        visible={assignUsersDialogVisible}
        onHide={() => setAssignUsersDialogVisible(false)}
        header={`Assign Users to ${warehouseToAssign?.Name || ''}`}
      >
        {filteredUsers && assignments ? (
          <div>
            {filteredUsers.map((user: any) => (
              <div
                key={user.UserId}
                className="flex items-center justify-between mb-2"
              >
                <span>
                  {user.Name} ({user.Email})
                </span>
                {isUserAssigned(user.UserId) ? (
                  <Button
                    label="Remove"
                    onClick={() => handleRemove(user.UserId)}
                    className="p-button-secondary"
                  />
                ) : (
                  <Button
                    label="Assign"
                    onClick={() => handleAssign(user.UserId)}
                  />
                )}
              </div>
            ))}
          </div>
        ) : (
          <p>Loading users...</p>
        )}
      </ReusableDialog>

      <ReusableToast ref={toastRef} />
      <ReusableConfirmDialog />
    </>
  );
}

export default WarehousesManagement;
