import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';

// Gets the total number of quarantined items in the database that are not soft-deleted.
export async function GET(request: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the database role as the source of truth
    const isSystemAdmin = userRecord.Role === 'system_admin';
    const isAdmin = userRecord.Role === 'admin';

    let quarantinedCount;

    // If user is system_admin, return all quarantined items count
    if (isSystemAdmin) {
      quarantinedCount = await prisma.item.count({
        where: { IsQuarantined: true, IsDeleted: false },
      });
    } else if (isAdmin) {
      // For admin, only count items from warehouses they have access to
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });

      const warehouseIds = userWarehouses.map(
        (uw: { WarehouseId: string }) => uw.WarehouseId
      );

      quarantinedCount = await prisma.item.count({
        where: {
          IsQuarantined: true,
          IsDeleted: false,
          WarehouseId: { in: warehouseIds },
        },
      });
    } else {
      // For non-admin users, return 403
      return NextResponse.json(
        { error: 'Forbidden: Only admins can access this resource' },
        { status: 403 }
      );
    }

    return NextResponse.json(quarantinedCount);
  } catch (error) {
    console.error('Error fetching quarantined items count:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
