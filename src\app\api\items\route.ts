import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { parsePackageTypes, stringifyPackageTypes } from '@/app/shared/utils/packageTypes.utils';
import { cookies } from 'next/headers';
import { getNextReceiptNumber } from './utils/items.helper';
import { getToken } from 'next-auth/jwt';

// GET /api/items
// GET /api/items?warehouseId=...
// GET /api/items?userId=...
export async function GET(req: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;
    let userRole: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
        userRole = cookieStore.get('userRole')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
      userRole = token.role as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const role = userRecord.Role;
    const isAdmin = role === 'admin';

    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    if (action === 'nextReceiptNumber') {
      const candidateNumber = await getNextReceiptNumber();
      return NextResponse.json({ receiptNumber: candidateNumber });
    }

    const queryWarehouseId = searchParams.get('warehouseId');

    let conditions: any = { IsDeleted: false };
    if (queryWarehouseId) {
      conditions.WarehouseId = queryWarehouseId;
    }

    if (!isAdmin) {
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });
      const allowedWarehouseIds = userWarehouses.map((uw) => uw.WarehouseId);
      if (queryWarehouseId) {
        if (!allowedWarehouseIds.includes(queryWarehouseId)) {
          return NextResponse.json(
            { error: 'Unauthorized: You do not have access to this warehouse' },
            { status: 403 }
          );
        }
      } else {
        conditions.WarehouseId = { in: allowedWarehouseIds };
      }
    }

    const items = await prisma.item.findMany({
      where: conditions,
      include: {
        Warehouse: true,
        ReceivedUser: {
          select: { Name: true },
        },
        Asset: true,
        Vendor: true,
        Vessel: true,
      },
    });

    const sanitizedItems = items.map((item) => ({
      ...item,
      // Parse packageType JSON string to array if it's a valid JSON string
      packageType: parsePackageTypes(item.packageType),
      Asset: item.Asset || {},
      Vessel: item.Vessel || {},
    }));

    return NextResponse.json(sanitizedItems);
  } catch (error) {
    console.error('Error fetching items:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// POST: create a new item
export async function POST(req: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const role = userRecord.Role;

    if (role === 'read') {
      return NextResponse.json(
        { error: 'Forbidden: read-only users cannot create items' },
        { status: 403 }
      );
    }

    const data = await req.json();
    // Validate ReceiptNumber format
    if (!/^A\d{4}$/.test(data.ReceiptNumber)) {
      return NextResponse.json(
        { error: 'Invalid Receipt Number format' },
        { status: 400 }
      );
    }
    // Check for uniqueness of ReceiptNumber
    const existingItem = await prisma.item.findUnique({
      where: { ReceiptNumber: data.ReceiptNumber },
    });
    if (existingItem) {
      return NextResponse.json(
        { error: 'Receipt Number already exists' },
        { status: 403 }
      );
    }

    // Validate GUID fields: converting empty strings to null so they can be properly cast
    const guidKeys = [
      'ItemId',
      'WarehouseId',
      'AssetId',
      'VendorId',
      'VesselId',
      'ReceivedByUserId',
    ];
    guidKeys.forEach((key) => {
      if (data[key] === '') {
        data[key] = null;
      }
    });

    // Convert packageType array to JSON string
    data.packageType = stringifyPackageTypes(data.packageType);

    const item = await prisma.item.create({
      data,
      include: {
        Warehouse: true,
        ReceivedUser: {
          select: { Name: true },
        },
        Asset: true,
        Vendor: true,
        Vessel: true,
      },
    });
    return NextResponse.json(item, { status: 201 });
  } catch (error) {
    console.error('Error creating item:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT: update item (soft delete)
export async function PUT(req: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const role = userRecord.Role;

    if (role === 'read') {
      return NextResponse.json(
        { error: 'Forbidden: read-only users cannot update items' },
        { status: 403 }
      );
    }

    const { ItemId, isDeleted } = await req.json();
    if (!ItemId) {
      return NextResponse.json(
        { error: 'ItemId is required' },
        { status: 400 }
      );
    }
    const item = await prisma.item.update({
      where: { ItemId },
      data: { IsDeleted: isDeleted },
      include: {
        Warehouse: true,
        ReceivedUser: { select: { Name: true } },
        Asset: true,
        Vendor: true,
        Vessel: true,
      },
    });
    return NextResponse.json(item);
  } catch (error) {
    console.error('Error updating item:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
