// api/admin/users

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import { isValidEmail } from '@/lib/validation';

// Helper function to check if user is authenticated and has admin or system_admin role
async function isAuthorizedAdmin(req: NextRequest) {
  try {
    // Get the token from the request
    const token = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (!token || !token.email) {
      return false;
    }

    // Check if the user exists and has admin or system_admin role
    const user = await prisma.user.findFirst({
      where: {
        Email: token.email,
        IsDeleted: false,
      },
    });

    if (!user || (user.Role !== 'admin' && user.Role !== 'system_admin')) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

export async function GET(req: NextRequest) {
  try {
    // Get the token from the request
    const token = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (!token || !token.email) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Get the user from the database
    const requester = await prisma.user.findFirst({
      where: {
        Email: token.email,
        IsDeleted: false,
      },
    });

    if (!requester) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Check if user is authorized as admin or system_admin
    if (requester.Role !== 'admin' && requester.Role !== 'system_admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // If user is system_admin, return all users
    if (requester.Role === 'system_admin') {
      const users = await prisma.user.findMany({
        where: { IsDeleted: false },
        orderBy: { Name: 'asc' },
      });
      return NextResponse.json(users, { status: 200 });
    }

    // If user is admin, only return users who are in the same warehouses
    // First, get the warehouses the admin is assigned to
    const adminWarehouses = await prisma.userWarehouse.findMany({
      where: { UserId: requester.UserId },
      select: { WarehouseId: true },
    });

    const warehouseIds = adminWarehouses.map((w) => w.WarehouseId);

    // Get all users assigned to those warehouses
    const usersInWarehouses = await prisma.userWarehouse.findMany({
      where: {
        WarehouseId: { in: warehouseIds },
      },
      select: { UserId: true },
    });

    // Create an array of unique user IDs
    const userIds = Array.from(new Set(usersInWarehouses.map((u) => u.UserId)));

    // Also include users with no warehouse assignments (they might need to be assigned)
    const usersWithNoWarehouses = await prisma.user.findMany({
      where: {
        IsDeleted: false,
        NOT: {
          UserId: {
            in: await prisma.userWarehouse
              .findMany()
              .then((uws) => uws.map((uw) => uw.UserId)),
          },
        },
        // Don't include system_admin users
        Role: { not: 'system_admin' },
      },
      select: { UserId: true },
    });

    userIds.push(...usersWithNoWarehouses.map((u) => u.UserId));

    // Get the full user records
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { UserId: { in: userIds } },
          { UserId: requester.UserId }, // Always include the current user
        ],
        IsDeleted: false,
      },
      orderBy: { Name: 'asc' },
    });

    return NextResponse.json(users, { status: 200 });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the token from the request
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (!token || !token.email) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Get the user from the database
    const requester = await prisma.user.findFirst({
      where: {
        Email: token.email,
        IsDeleted: false,
      },
    });

    if (
      !requester ||
      (requester.Role !== 'admin' && requester.Role !== 'system_admin')
    ) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    const { Name, Email, Role } = await request.json();
    // Validate all fields are provided
    if (!Name || !Email || !Role) {
      return NextResponse.json(
        { error: 'All fields are mandatory' },
        { status: 400 }
      );
    }
    // Add regex check here
    if (!isValidEmail(Email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }
    // Only system_admin can create system_admin users
    if (Role === 'system_admin' && requester.Role !== 'system_admin') {
      return NextResponse.json(
        {
          error:
            'Only system administrators can create system administrator accounts',
        },
        { status: 403 }
      );
    }

    // Validate Role is one of the allowed values
    if (!['admin', 'read', 'write', 'system_admin'].includes(Role)) {
      return NextResponse.json(
        { error: 'Role must be one of system_admin, admin, read, or write' },
        { status: 400 }
      );
    }
    const newUser = await prisma.user.create({
      data: { Name, Email, Role },
    });
    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
