import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import { Prisma } from '@prisma/client';

// @important Prisma escapes values internally when used like this
// @important No user input should be interpolated into the SQL.
export async function GET(request: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the database role as the source of truth
    const isSystemAdmin = userRecord.Role === 'system_admin';
    const isAdmin = userRecord.Role === 'admin';

    // For admin users, get the list of warehouse IDs they have access to
    let warehouseFilter = {};
    if (!isSystemAdmin && isAdmin) {
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });

      const warehouseIds = userWarehouses.map(
        (uw: { WarehouseId: string }) => uw.WarehouseId
      );

      if (warehouseIds.length === 0) {
        // If admin has no warehouses, return empty stats
        return NextResponse.json({
          monthlyStats: [],
          monthlyStatsByWarehouse: [],
          quarantinedStats: { Pending: 0, Closed: 0 },
          loadListStats: [],
        });
      }

      warehouseFilter = { WarehouseId: { in: warehouseIds } };
    }
    // Different SQL query based on user role
    let monthlyStats;
    if (isSystemAdmin) {
      monthlyStats = await prisma.$queryRaw<
        { Year: number; Month: number; Count: number }[]
      >`
        SELECT YEAR(ReceiptDateTime) as Year,
               MONTH(ReceiptDateTime) as Month,
               COUNT(*) as Count
        FROM Item
        WHERE IsDeleted = 0 AND WarehouseId IS NOT NULL
        GROUP BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
        ORDER BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
      `;
    } else if (isAdmin) {
      // For admin, only count items in warehouses they have access to
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });

      const warehouseIds = userWarehouses.map(
        (uw: { WarehouseId: string }) => uw.WarehouseId
      );

      if (warehouseIds.length > 0) {
        // Create an array of warehouse IDs for the query
        const warehouseIdsArray = warehouseIds;

        monthlyStats = await prisma.$queryRaw<
          { Year: number; Month: number; Count: number }[]
        >`
          SELECT YEAR(ReceiptDateTime) as Year,
                 MONTH(ReceiptDateTime) as Month,
                 COUNT(*) as Count
          FROM Item
          WHERE IsDeleted = 0 AND WarehouseId IS NOT NULL 
                AND WarehouseId IN (${Prisma.join(warehouseIdsArray)})
          GROUP BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
          ORDER BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
        `;
      } else {
        monthlyStats = [];
      }
    }
    // Different SQL query for monthlyStatsByWarehouse based on user role
    let monthlyStatsByWarehouse;
    if (isSystemAdmin) {
      monthlyStatsByWarehouse = await prisma.$queryRaw`
        SELECT w.Name as WarehouseName,
               YEAR(i.ReceiptDateTime) as Year,
               MONTH(i.ReceiptDateTime) as Month,
               COUNT(*) as Count
        FROM Item i
        JOIN Warehouse w ON w.WarehouseId = i.WarehouseId
        WHERE i.IsDeleted = 0
        GROUP BY w.Name, YEAR(i.ReceiptDateTime), MONTH(i.ReceiptDateTime)
        ORDER BY w.Name, YEAR(i.ReceiptDateTime), MONTH(i.ReceiptDateTime)
      `;
    } else if (isAdmin) {
      // For admin, only include warehouses they have access to
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });

      const warehouseIds = userWarehouses.map(
        (uw: { WarehouseId: string }) => uw.WarehouseId
      );

      if (warehouseIds.length > 0) {
        // Create an array of warehouse IDs for the query
        const warehouseIdsArray = warehouseIds;

        monthlyStatsByWarehouse = await prisma.$queryRaw`
          SELECT w.Name as WarehouseName,
                 YEAR(i.ReceiptDateTime) as Year,
                 MONTH(i.ReceiptDateTime) as Month,
                 COUNT(*) as Count
          FROM Item i
          JOIN Warehouse w ON w.WarehouseId = i.WarehouseId
          WHERE i.IsDeleted = 0 AND i.WarehouseId IN (${Prisma.join(warehouseIdsArray)})
          GROUP BY w.Name, YEAR(i.ReceiptDateTime), MONTH(i.ReceiptDateTime)
          ORDER BY w.Name, YEAR(i.ReceiptDateTime), MONTH(i.ReceiptDateTime)
        `;
      } else {
        monthlyStatsByWarehouse = [];
      }
    }
    // Different SQL query for quarantinedStats based on user role
    let quarantinedStats;
    if (isSystemAdmin) {
      quarantinedStats = await prisma.$queryRaw<
        {
          Pending: number;
          Closed: number;
        }[]
      >`
        SELECT
          SUM(CASE WHEN [Status] = 'Pending' THEN 1 ELSE 0 END) as Pending,
          SUM(CASE WHEN [Status] = 'Resolved' THEN 1 ELSE 0 END) as Closed
        FROM Item
        WHERE IsQuarantined = 1 AND IsDeleted = 0 AND WarehouseId IS NOT NULL
      `;
    } else if (isAdmin) {
      // For admin, only count items in warehouses they have access to
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });

      const warehouseIds = userWarehouses.map(
        (uw: { WarehouseId: string }) => uw.WarehouseId
      );

      if (warehouseIds.length > 0) {
        // Create an array of warehouse IDs for the query
        const warehouseIdsArray = warehouseIds;

        quarantinedStats = await prisma.$queryRaw<
          {
            Pending: number;
            Closed: number;
          }[]
        >`
          SELECT
            SUM(CASE WHEN [Status] = 'Pending' THEN 1 ELSE 0 END) as Pending,
            SUM(CASE WHEN [Status] = 'Resolved' THEN 1 ELSE 0 END) as Closed
          FROM Item
          WHERE IsQuarantined = 1 AND IsDeleted = 0 AND WarehouseId IS NOT NULL
                AND WarehouseId IN (${Prisma.join(warehouseIdsArray)})
        `;
      } else {
        quarantinedStats = [{ Pending: 0, Closed: 0 }];
      }
    }

    // Different SQL query for loadListStats based on user role
    let loadListStats;
    if (isSystemAdmin) {
      loadListStats = await prisma.$queryRaw`
        SELECT YEAR(ReceiptDateTime) as Year,
               MONTH(ReceiptDateTime) as Month,
               COUNT(*) as Count
        FROM Item
        WHERE LoadListItem = 1 AND IsDeleted = 0 AND WarehouseId IS NOT NULL
        GROUP BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
        ORDER BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
      `;
    } else if (isAdmin) {
      // For admin, only count items in warehouses they have access to
      const userWarehouses = await prisma.userWarehouse.findMany({
        where: { UserId: userRecord.UserId },
        select: { WarehouseId: true },
      });

      const warehouseIds = userWarehouses.map(
        (uw: { WarehouseId: string }) => uw.WarehouseId
      );

      if (warehouseIds.length > 0) {
        // Create an array of warehouse IDs for the query
        const warehouseIdsArray = warehouseIds;

        loadListStats = await prisma.$queryRaw`
          SELECT YEAR(ReceiptDateTime) as Year,
                 MONTH(ReceiptDateTime) as Month,
                 COUNT(*) as Count
          FROM Item
          WHERE LoadListItem = 1 AND IsDeleted = 0 AND WarehouseId IS NOT NULL
                AND WarehouseId IN (${Prisma.join(warehouseIdsArray)})
          GROUP BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
          ORDER BY YEAR(ReceiptDateTime), MONTH(ReceiptDateTime)
        `;
      } else {
        loadListStats = [];
      }
    }
    return NextResponse.json({
      monthlyStats,
      monthlyStatsByWarehouse,
      quarantinedStats: quarantinedStats
        ? quarantinedStats[0]
        : { Pending: 0, Closed: 0 },
      loadListStats,
    });
  } catch (error) {
    console.error('Error fetching monthly stats:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
